# MPhotos 项目初始化完整指令

## 第一步：创建Xcode项目

### 1.1 Xcode操作步骤
```
1. 打开 Xcode
2. 选择 "Create New Project"
3. 选择 iOS > App
4. 填写项目信息：
   - Product Name: MPhotos
   - Team: 选择你的开发者账号（如果没有选None）
   - Organization Identifier: com.mphotos
   - Bundle Identifier: com.mphotos.app（自动生成）
   - Interface: Storyboard
   - Language: Swift
   - Use Core Data: 不勾选
   - Include Tests: 勾选
5. 点击 Next，选择保存位置
6. 不要勾选 "Create Git repository"（我们手动创建）
```

### 1.2 删除不需要的文件
```bash
# 在项目目录下
rm -f MPhotos/Main.storyboard
rm -f MPhotos/ViewController.swift
```

## 第二步：配置项目设置

### 2.1 更新Info.plist
在 `MPhotos/Info.plist` 中添加以下内容（使用Xcode打开）：

```xml
<!-- 相册权限 -->
<key>NSPhotoLibraryUsageDescription</key>
<string>MPhotos需要访问您的照片库来帮助您管理和优化照片</string>

<key>NSPhotoLibraryAddUsageDescription</key>
<string>MPhotos需要保存照片到您的相册</string>

<!-- 仅支持竖屏 -->
<key>UISupportedInterfaceOrientations</key>
<array>
    <string>UIInterfaceOrientationPortrait</string>
</array>

<key>UISupportedInterfaceOrientations~ipad</key>
<array>
    <string>UIInterfaceOrientationPortrait</string>
</array>

<!-- 支持深色模式 -->
<key>UIUserInterfaceStyle</key>
<string>Automatic</string>

<!-- 删除Storyboard引用 -->
<!-- 删除这两行 -->
<!-- <key>UIMainStoryboardFile</key> -->
<!-- <string>Main</string> -->

<!-- 启动屏设置 -->
<key>UILaunchStoryboardName</key>
<string>LaunchScreen</string>
```

### 2.2 项目设置
1. 选择项目 -> MPhotos Target
2. General标签：
   - Minimum Deployments: iOS 16.0
   - Device Orientation: 只勾选 Portrait
3. 删除 Main Interface 中的 "Main"

## 第三步：创建文件结构

### 3.1 创建目录
```bash
cd MPhotos
mkdir -p MPhotos/{App,Core,Features/{PhotoGrid,Albums,Cleanup,Tools},Utilities,Resources,SupportingFiles}
```

### 3.2 创建文档文件
```bash
# 在项目根目录
touch README.md
touch PROGRESS.md
touch ARCHITECTURE.md
touch CODE_TEMPLATES.md
touch API_REFERENCE.md
touch REQUIREMENTS.md
```

## 第四步：创建基础代码文件

### 4.1 更新 AppDelegate.swift
```swift
import UIKit

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // 全局UI配置
        configureAppearance()
        return true
    }

    // MARK: UISceneSession Lifecycle
    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }

    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
    }
    
    // MARK: - Private Methods
    private func configureAppearance() {
        // 导航栏全局设置
        if #available(iOS 15.0, *) {
            let appearance = UINavigationBarAppearance()
            appearance.configureWithDefaultBackground()
            UINavigationBar.appearance().standardAppearance = appearance
            UINavigationBar.appearance().scrollEdgeAppearance = appearance
        }
    }
}
```

### 4.2 更新 SceneDelegate.swift
```swift
import UIKit

class SceneDelegate: UIResponder, UIWindowSceneDelegate {

    var window: UIWindow?

    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        guard let windowScene = (scene as? UIWindowScene) else { return }
        
        window = UIWindow(windowScene: windowScene)
        window?.rootViewController = MainTabBarController()
        window?.makeKeyAndVisible()
    }

    func sceneDidDisconnect(_ scene: UIScene) {
    }

    func sceneDidBecomeActive(_ scene: UIScene) {
    }

    func sceneWillResignActive(_ scene: UIScene) {
    }

    func sceneWillEnterForeground(_ scene: UIScene) {
    }

    func sceneDidEnterBackground(_ scene: UIScene) {
    }
}
```

### 4.3 创建 MainTabBarController.swift
在 `MPhotos/App/` 目录下创建：

```swift
import UIKit

class MainTabBarController: UITabBarController {
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupViewControllers()
        setupAppearance()
    }
    
    private func setupViewControllers() {
        // 图库
        let photoGridVC = createNavController(
            rootVC: createPlaceholderVC(title: "图库"),
            title: "图库",
            imageName: "photo.on.rectangle"
        )
        
        // 整理
        let cleanupVC = createNavController(
            rootVC: createPlaceholderVC(title: "整理"),
            title: "整理",
            imageName: "sparkles"
        )
        
        // 相簿
        let albumsVC = createNavController(
            rootVC: createPlaceholderVC(title: "相簿"),
            title: "相簿",
            imageName: "rectangle.stack"
        )
        
        // 功能
        let toolsVC = createNavController(
            rootVC: createPlaceholderVC(title: "功能"),
            title: "功能",
            imageName: "ellipsis.circle"
        )
        
        viewControllers = [photoGridVC, cleanupVC, albumsVC, toolsVC]
    }
    
    private func createNavController(rootVC: UIViewController, title: String, imageName: String) -> UINavigationController {
        let navController = UINavigationController(rootViewController: rootVC)
        navController.tabBarItem.title = title
        navController.tabBarItem.image = UIImage(systemName: imageName)
        navController.navigationBar.prefersLargeTitles = true
        rootVC.navigationItem.title = title
        return navController
    }
    
    private func createPlaceholderVC(title: String) -> UIViewController {
        let vc = UIViewController()
        vc.view.backgroundColor = .systemBackground
        
        // 添加占位标签
        let label = UILabel()
        label.text = "\(title)功能开发中..."
        label.textAlignment = .center
        label.textColor = .secondaryLabel
        label.translatesAutoresizingMaskIntoConstraints = false
        
        vc.view.addSubview(label)
        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: vc.view.centerXAnchor),
            label.centerYAnchor.constraint(equalTo: vc.view.centerYAnchor)
        ])
        
        return vc
    }
    
    private func setupAppearance() {
        // Tab Bar 外观设置
        tabBar.tintColor = .systemBlue
        
        if #available(iOS 15.0, *) {
            let appearance = UITabBarAppearance()
            appearance.configureWithDefaultBackground()
            tabBar.standardAppearance = appearance
            tabBar.scrollEdgeAppearance = appearance
        }
    }
}
```

## 第五步：初始化Git仓库

```bash
# 在项目根目录
git init

# 创建.gitignore
cat > .gitignore << 'EOF'
# Xcode
#
# gitignore contributors: remember to update Global/Xcode.gitignore, Objective-C.gitignore & Swift.gitignore

## User settings
xcuserdata/

## compatibility with Xcode 8 and earlier (ignoring not required starting Xcode 9)
*.xcscmblueprint
*.xccheckout

## compatibility with Xcode 3 and earlier (ignoring not required starting Xcode 4)
build/
DerivedData/
*.moved-aside
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3

## Obj-C/Swift specific
*.hmap

## App packaging
*.ipa
*.dSYM.zip
*.dSYM

## Playgrounds
timeline.xctimeline
playground.xcworkspace

# Swift Package Manager
.build/

# CocoaPods
Pods/

# Carthage
Carthage/Build/

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/**/*.png
fastlane/test_output

# Code Injection
*.xcworkspace

# macOS
.DS_Store
EOF

# 初始提交
git add .
git commit -m "Initial commit: MPhotos project setup with TabBar structure"
```

## 第六步：添加项目到Xcode

1. 将创建的文件添加到Xcode项目：
   - 右键点击 MPhotos 文件夹（在Xcode中）
   - 选择 "Add Files to MPhotos..."
   - 选择 App 文件夹，确保 "Create groups" 被选中
   - 重复添加其他文件夹

2. 组织项目结构：
   - 将 AppDelegate.swift 和 SceneDelegate.swift 移到 App 组
   - 删除原来的 ViewController.swift 引用

## 第七步：运行验证

### 7.1 编译运行
```
1. 选择模拟器（建议 iPhone 15）
2. 点击运行按钮或按 Cmd+R
3. 应该看到带有4个Tab的界面
```

### 7.2 验证清单
- [ ] 项目能成功编译
- [ ] 能在模拟器运行
- [ ] 显示4个Tab，每个Tab显示占位文本
- [ ] Tab图标正确显示
- [ ] 支持深色模式切换
- [ ] Git仓库创建成功

## 第八步：创建所有文档

将之前生成的文档内容保存到对应文件：
- README.md
- PROGRESS.md
- ARCHITECTURE.md
- CODE_TEMPLATES.md
- API_REFERENCE.md
- REQUIREMENTS.md

## 完成后的项目结构

```
MPhotos/
├── MPhotos.xcodeproj
├── MPhotos/
│   ├── App/
│   │   ├── AppDelegate.swift
│   │   ├── SceneDelegate.swift
│   │   └── MainTabBarController.swift
│   ├── Core/
│   ├── Features/
│   │   ├── PhotoGrid/
│   │   ├── Albums/
│   │   ├── Cleanup/
│   │   └── Tools/
│   ├── Utilities/
│   ├── Resources/
│   │   ├── Assets.xcassets
│   │   └── LaunchScreen.storyboard
│   └── Info.plist
├── MPhotosTests/
├── MPhotosUITests/
├── README.md
├── PROGRESS.md
├── ARCHITECTURE.md
├── CODE_TEMPLATES.md
├── API_REFERENCE.md
├── REQUIREMENTS.md
└── .gitignore
```

## 下一步

完成初始化后，在PROGRESS.md中更新Task 1.1的状态为"已完成"，然后说：
```
编译成功，Task 1.1完成。我要继续开发。
```

系统将自动进入Task 1.2的开发。

## 常见问题

### Q: 编译错误 "No such module 'UIKit'"
A: 确保选择的是iOS项目，不是macOS项目

### Q: Tab图标不显示
A: 确保使用的是SF Symbols中存在的图标名称

### Q: 无法运行在真机上
A: 需要配置开发者账号和证书，初期建议使用模拟器

---

**提示**：严格按照步骤操作，每一步都要验证成功后再继续。